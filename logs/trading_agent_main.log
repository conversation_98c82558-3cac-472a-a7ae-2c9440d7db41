2025-06-09 12:23:25.729 | INFO     | utils.logging:setup_logging:153 | [PID:447196] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 12:23:25.730 | INFO     | utils.logging:setup_logging:157 | [PID:447196] Worker logging setup complete - Worker ID: main
2025-06-09 12:23:25.730 | INFO     | __main__:cli:98 | FinRL Trading Agent CLI initialized
2025-06-09 12:23:25.731 | DEBUG    | __main__:cli:99 | Configuration loaded from: default
2025-06-09 12:23:30.937 | INFO     | __main__:tune:312 | Starting hyperparameter tuning
2025-06-09 12:23:31.147 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 12:23:31.147 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 12:23:31.149 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 12:23:31.168 | INFO     | utils.logging:info:191 | [PID:447196] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:23:33.457 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 12:23:33.460 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 12:23:33.480 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.019s
2025-06-09 12:23:33.480 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 12:23:33.480 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.020s
2025-06-09 12:23:33.481 | INFO     | utils.logging:info:191 | [PID:447196] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:23:33.481 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 12:23:33.494 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 12:23:33.494 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.014s
2025-06-09 12:23:33.496 | INFO     | utils.logging:info:191 | [PID:447196] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:23:33.507 | INFO     | utils.logging:info:191 | [PID:447196] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 12:23:33.513 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 12:23:33.514 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 12:23:33.541 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 12:23:33.597 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 12:23:33.597 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.448s
2025-06-09 12:23:33.598 | INFO     | __main__:tune:333 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-09 12:23:33.598 | INFO     | __main__:tune:334 | Shape: (23370, 45)
2025-06-09 12:23:33.598 | INFO     | __main__:tune:336 | Columns: ['date', 'open', 'high', 'low', 'close', 'volume', 'dividends', 'stock splits', 'tic', 'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'rsi_14', 'cci_20', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'turbulence', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:23:33.607 | INFO     | __main__:tune:339 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:23:33.607 | INFO     | __main__:tune:340 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:23:33.608 | INFO     | __main__:tune:342 | Date range in processed_for_env_df ('date'): 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 12:23:33.610 | INFO     | __main__:tune:344 | Unique 'tic' in processed_for_env_df: 10
2025-06-09 12:23:33.610 | INFO     | __main__:tune:347 | --- End Quick Stats for processed_for_env_df ---
2025-06-09 12:23:33.610 | INFO     | __main__:tune:358 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 12:23:33.618 | INFO     | __main__:tune:377 | Training data: 18690 records, Validation data: 4680 records
2025-06-09 12:23:33.618 | INFO     | __main__:tune:424 | Transforming data format for FinRL environment compatibility
2025-06-09 12:23:33.639 | INFO     | __main__:tune:428 | Train_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:23:33.641 | INFO     | __main__:tune:430 | Train_df columns after deduplication: 45
2025-06-09 12:23:33.642 | INFO     | __main__:tune:432 | Val_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:23:33.643 | INFO     | __main__:tune:434 | Val_df columns after deduplication: 45
2025-06-09 12:23:33.643 | INFO     | __main__:tune:436 | Transformed training data: 18690 records, Validation data: 4680 records
2025-06-09 12:23:33.643 | INFO     | __main__:tune:447 | Training data index: day, shape: (18690, 45)
2025-06-09 12:23:33.643 | INFO     | __main__:tune:448 | Validation data index: day, shape: (4680, 45)
2025-06-09 12:23:33.644 | INFO     | __main__:tune:457 | ✓ training data has correct 'day' index for FinRL (range: 0 to 1868)
2025-06-09 12:23:33.644 | INFO     | __main__:tune:457 | ✓ validation data has correct 'day' index for FinRL (range: 0 to 467)
2025-06-09 12:23:33.644 | INFO     | __main__:tune:460 | Applying NaN fill and type check for technical indicators in training data.
2025-06-09 12:23:33.660 | INFO     | __main__:tune:475 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-09 12:23:33.705 | INFO     | __main__:tune:588 | Environment created: state_dim=431, action_dim=10
2025-06-09 12:23:33.705 | INFO     | utils.logging:__enter__:344 | Starting Hyperparameter Optimization | Context: 
2025-06-09 12:23:33.705 | INFO     | utils.logging:info:191 | Starting optimization with config: {'n_trials': 1, 'timeout': None, 'n_jobs': 1, 'sampler': 'tpe', 'pruner': 'median', 'direction': 'maximize', 'study_name': 'sac_trading_optimization', 'storage': 'sqlite:///optuna_studies.db', 'load_if_exists': False, 'directions': None, 'pruning_warmup_steps': 10, 'pruning_interval_steps': 50, 'early_stopping_rounds': None, 'early_stopping_threshold': None, 'learning_rate_range': (1e-05, 0.01), 'batch_size_choices': [64, 128, 256, 512], 'gamma_range': (0.9, 0.999), 'tau_range': (0.001, 0.01), 'alpha_range': (0.1, 0.5), 'hidden_sizes_choices': [[128, 128], [256, 256], [512, 256], [256, 256, 128]]}
2025-06-09 12:23:34.053 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.002048338123746334, 'batch_size': 64, 'net_dims': [256, 256], 'gamma': 0.9581171309354973, 'soft_update_tau': 0.0018737707270658369, 'alpha': 0.2008779633629103}
2025-06-09 12:23:34.053 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:23:34.054 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:23:34.054 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:23:35.369 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [256, 256], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9581171309354973, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.002048338123746334, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 10000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:23:35.369 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:23:35.376 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:23:35.376 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:23:35.377 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:23:35.377 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:23:35.377 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:23:35.377 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:23:35.378 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:23:35.378 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:23:35.378 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:24:29.951 | ERROR    | utils.logging:__exit__:354 | Failed Hyperparameter Optimization after 56.246s: 
2025-06-09 12:25:35.874 | INFO     | utils.logging:setup_logging:153 | [PID:448652] Logging initialized - Level: DEBUG, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 12:25:35.874 | INFO     | utils.logging:setup_logging:157 | [PID:448652] Worker logging setup complete - Worker ID: main
2025-06-09 12:25:35.874 | INFO     | __main__:cli:98 | FinRL Trading Agent CLI initialized
2025-06-09 12:25:35.874 | DEBUG    | __main__:cli:99 | Configuration loaded from: default
2025-06-09 12:25:38.692 | INFO     | __main__:tune:312 | Starting hyperparameter tuning
2025-06-09 12:25:38.827 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 12:25:38.828 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 12:25:38.830 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 12:25:38.848 | INFO     | utils.logging:info:191 | [PID:448652] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:25:40.808 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 12:25:40.812 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 12:25:40.843 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.031s
2025-06-09 12:25:40.844 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 12:25:40.844 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.033s
2025-06-09 12:25:40.844 | INFO     | utils.logging:info:191 | [PID:448652] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:25:40.845 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 12:25:40.853 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 12:25:40.853 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.009s
2025-06-09 12:25:40.855 | INFO     | utils.logging:info:191 | [PID:448652] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:25:40.863 | INFO     | utils.logging:info:191 | [PID:448652] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 12:25:40.870 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 12:25:40.871 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 12:25:40.903 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 12:25:40.942 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 12:25:40.943 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.113s
2025-06-09 12:25:40.944 | INFO     | __main__:tune:333 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-09 12:25:40.944 | INFO     | __main__:tune:334 | Shape: (23370, 45)
2025-06-09 12:25:40.945 | INFO     | __main__:tune:336 | Columns: ['date', 'open', 'high', 'low', 'close', 'volume', 'dividends', 'stock splits', 'tic', 'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'rsi_14', 'cci_20', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'turbulence', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:25:40.957 | INFO     | __main__:tune:339 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:25:40.958 | INFO     | __main__:tune:340 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:25:40.959 | INFO     | __main__:tune:342 | Date range in processed_for_env_df ('date'): 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 12:25:40.960 | INFO     | __main__:tune:344 | Unique 'tic' in processed_for_env_df: 10
2025-06-09 12:25:40.960 | INFO     | __main__:tune:347 | --- End Quick Stats for processed_for_env_df ---
2025-06-09 12:25:40.960 | INFO     | __main__:tune:358 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 12:25:40.969 | INFO     | __main__:tune:377 | Training data: 18690 records, Validation data: 4680 records
2025-06-09 12:25:40.969 | INFO     | __main__:tune:424 | Transforming data format for FinRL environment compatibility
2025-06-09 12:25:40.991 | INFO     | __main__:tune:428 | Train_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:25:40.993 | INFO     | __main__:tune:430 | Train_df columns after deduplication: 45
2025-06-09 12:25:40.993 | INFO     | __main__:tune:432 | Val_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:25:40.994 | INFO     | __main__:tune:434 | Val_df columns after deduplication: 45
2025-06-09 12:25:40.995 | INFO     | __main__:tune:436 | Transformed training data: 18690 records, Validation data: 4680 records
2025-06-09 12:25:40.995 | INFO     | __main__:tune:447 | Training data index: day, shape: (18690, 45)
2025-06-09 12:25:40.995 | INFO     | __main__:tune:448 | Validation data index: day, shape: (4680, 45)
2025-06-09 12:25:40.996 | INFO     | __main__:tune:457 | ✓ training data has correct 'day' index for FinRL (range: 0 to 1868)
2025-06-09 12:25:40.996 | INFO     | __main__:tune:457 | ✓ validation data has correct 'day' index for FinRL (range: 0 to 467)
2025-06-09 12:25:40.996 | INFO     | __main__:tune:460 | Applying NaN fill and type check for technical indicators in training data.
2025-06-09 12:25:41.012 | INFO     | __main__:tune:475 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-09 12:25:41.051 | INFO     | __main__:tune:588 | Environment created: state_dim=431, action_dim=10
2025-06-09 12:25:41.051 | INFO     | utils.logging:__enter__:344 | Starting Hyperparameter Optimization | Context: 
2025-06-09 12:25:41.052 | INFO     | utils.logging:info:191 | Starting optimization with config: {'n_trials': 1, 'timeout': None, 'n_jobs': 1, 'sampler': 'tpe', 'pruner': 'median', 'direction': 'maximize', 'study_name': 'sac_trading_optimization', 'storage': 'sqlite:///optuna_studies.db', 'load_if_exists': False, 'directions': None, 'pruning_warmup_steps': 10, 'pruning_interval_steps': 50, 'early_stopping_rounds': None, 'early_stopping_threshold': None, 'learning_rate_range': (1e-05, 0.01), 'batch_size_choices': [64, 128, 256, 512], 'gamma_range': (0.9, 0.999), 'tau_range': (0.001, 0.01), 'alpha_range': (0.1, 0.5), 'hidden_sizes_choices': [[128, 128], [256, 256], [512, 256], [256, 256, 128]]}
2025-06-09 12:25:41.305 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 5.045271367090511e-05, 'batch_size': 64, 'net_dims': [128, 128], 'gamma': 0.9545576827127897, 'soft_update_tau': 0.007437101466013946, 'alpha': 0.2752595796291375}
2025-06-09 12:25:41.305 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:25:41.306 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:25:41.306 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:25:42.182 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 128], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9545576827127897, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 5.045271367090511e-05, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 10000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:25:42.182 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:25:42.189 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:25:42.190 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:25:42.190 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:25:42.190 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:25:42.190 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:25:42.190 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:25:42.191 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:25:42.191 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:25:42.191 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:25:53.118 | ERROR    | utils.logging:__exit__:354 | Failed Hyperparameter Optimization after 12.066s: 
2025-06-09 12:27:01.945 | INFO     | utils.logging:setup_logging:153 | [PID:449880] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-09 12:27:01.945 | INFO     | utils.logging:setup_logging:157 | [PID:449880] Worker logging setup complete - Worker ID: main
2025-06-09 12:27:01.946 | INFO     | __main__:cli:98 | FinRL Trading Agent CLI initialized
2025-06-09 12:27:04.790 | INFO     | __main__:tune:312 | Starting hyperparameter tuning
2025-06-09 12:27:04.919 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-09 12:27:04.919 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-09 12:27:04.921 | INFO     | utils.logging:info:191 | Processing stock data: 23370 records
2025-06-09 12:27:04.938 | INFO     | utils.logging:info:191 | [PID:449880] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:27:06.857 | INFO     | utils.logging:info:191 | Added turbulence index: mean=1.1040, max=32.7216
2025-06-09 12:27:06.861 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-09 12:27:06.881 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.021s
2025-06-09 12:27:06.882 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-09 12:27:06.882 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.022s
2025-06-09 12:27:06.883 | INFO     | utils.logging:info:191 | [PID:449880] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-09 12:27:06.883 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-09 12:27:06.894 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-09 12:27:06.895 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.012s
2025-06-09 12:27:06.897 | INFO     | utils.logging:info:191 | [PID:449880] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:27:06.906 | INFO     | utils.logging:info:191 | [PID:449880] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-09 12:27:06.913 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-09 12:27:06.913 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-09 12:27:06.936 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-09 12:27:06.968 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 23370 records, 45 features
2025-06-09 12:27:06.968 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.047s
2025-06-09 12:27:06.969 | INFO     | __main__:tune:333 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-09 12:27:06.969 | INFO     | __main__:tune:334 | Shape: (23370, 45)
2025-06-09 12:27:06.969 | INFO     | __main__:tune:336 | Columns: ['date', 'open', 'high', 'low', 'close', 'volume', 'dividends', 'stock splits', 'tic', 'sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'rsi_14', 'cci_20', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'turbulence', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-09 12:27:06.978 | INFO     | __main__:tune:339 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:27:06.978 | INFO     | __main__:tune:340 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-09 12:27:06.979 | INFO     | __main__:tune:342 | Date range in processed_for_env_df ('date'): 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-09 12:27:06.980 | INFO     | __main__:tune:344 | Unique 'tic' in processed_for_env_df: 10
2025-06-09 12:27:06.980 | INFO     | __main__:tune:347 | --- End Quick Stats for processed_for_env_df ---
2025-06-09 12:27:06.980 | INFO     | __main__:tune:358 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-09 12:27:06.987 | INFO     | __main__:tune:377 | Training data: 18690 records, Validation data: 4680 records
2025-06-09 12:27:06.988 | INFO     | __main__:tune:424 | Transforming data format for FinRL environment compatibility
2025-06-09 12:27:07.012 | INFO     | __main__:tune:428 | Train_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:27:07.015 | INFO     | __main__:tune:430 | Train_df columns after deduplication: 45
2025-06-09 12:27:07.015 | INFO     | __main__:tune:432 | Val_df columns before deduplication (showing only duplicated ones): []
2025-06-09 12:27:07.017 | INFO     | __main__:tune:434 | Val_df columns after deduplication: 45
2025-06-09 12:27:07.017 | INFO     | __main__:tune:436 | Transformed training data: 18690 records, Validation data: 4680 records
2025-06-09 12:27:07.018 | INFO     | __main__:tune:447 | Training data index: day, shape: (18690, 45)
2025-06-09 12:27:07.018 | INFO     | __main__:tune:448 | Validation data index: day, shape: (4680, 45)
2025-06-09 12:27:07.018 | INFO     | __main__:tune:457 | ✓ training data has correct 'day' index for FinRL (range: 0 to 1868)
2025-06-09 12:27:07.019 | INFO     | __main__:tune:457 | ✓ validation data has correct 'day' index for FinRL (range: 0 to 467)
2025-06-09 12:27:07.019 | INFO     | __main__:tune:460 | Applying NaN fill and type check for technical indicators in training data.
2025-06-09 12:27:07.035 | INFO     | __main__:tune:475 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-09 12:27:07.062 | INFO     | __main__:tune:588 | Environment created: state_dim=431, action_dim=10
2025-06-09 12:27:07.062 | INFO     | utils.logging:__enter__:344 | Starting Hyperparameter Optimization | Context: 
2025-06-09 12:27:07.063 | INFO     | utils.logging:info:191 | Starting optimization with config: {'n_trials': 1, 'timeout': None, 'n_jobs': 1, 'sampler': 'tpe', 'pruner': 'median', 'direction': 'maximize', 'study_name': 'sac_trading_optimization', 'storage': 'sqlite:///optuna_studies.db', 'load_if_exists': False, 'directions': None, 'pruning_warmup_steps': 10, 'pruning_interval_steps': 50, 'early_stopping_rounds': None, 'early_stopping_threshold': None, 'learning_rate_range': (1e-05, 0.01), 'batch_size_choices': [64, 128, 256, 512], 'gamma_range': (0.9, 0.999), 'tau_range': (0.001, 0.01), 'alpha_range': (0.1, 0.5), 'hidden_sizes_choices': [[128, 128], [256, 256], [512, 256], [256, 256, 128]]}
2025-06-09 12:27:07.298 | INFO     | utils.logging:info:191 | Applied additional config overrides: {'learning_rate': 0.0011473869060058552, 'batch_size': 64, 'net_dims': [128, 128], 'gamma': 0.9604981303694807, 'soft_update_tau': 0.0024106226951120875, 'alpha': 0.20380939253220626}
2025-06-09 12:27:07.299 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=431, action_dim=10, device=cpu
2025-06-09 12:27:07.299 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-09 12:27:07.299 | INFO     | utils.logging:info:191 | SACAgent _create_agent: Determined gpu_id_to_pass: -1 from device setting: cpu
2025-06-09 12:27:08.123 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': [128, 128], 'state_dim': 431, 'action_dim': 10, 'gpu_id': -1, 'args': {'num_envs': 1, 'agent_class': None, 'if_off_policy': True, 'env_class': None, 'env_args': None, 'env_name': None, 'max_step': 12345, 'state_dim': None, 'action_dim': None, 'if_discrete': None, 'gamma': 0.9604981303694807, 'reward_scale': 1, 'net_dims': [128, 128], 'learning_rate': 0.0011473869060058552, 'clip_grad_norm': 3.0, 'state_value_tau': 0, 'soft_update_tau': 0.005, 'continue_train': False, 'batch_size': 64, 'horizon_len': 512, 'buffer_size': 1000000, 'repeat_times': 1.0, 'if_use_per': False, 'lambda_fit_cum_r': 0.0, 'buffer_init_size': 512, 'gpu_id': 0, 'num_workers': 2, 'num_threads': 8, 'random_seed': None, 'learner_gpu_ids': (), 'cwd': None, 'if_remove': True, 'break_step': 10000, 'break_score': inf, 'if_keep_save': True, 'if_over_write': False, 'if_save_buffer': False, 'save_gap': 8, 'eval_times': 3, 'eval_per_step': 20000, 'eval_env_class': None, 'eval_env_args': None, 'eval_record_step': 0, 'target_entropy': -10.0}}
2025-06-09 12:27:08.123 | INFO     | utils.logging:info:191 | Starting SAC training: 10000 timesteps
2025-06-09 12:27:08.126 | INFO     | utils.logging:info:191 | SACAgent train: Set ElegantRL config.gpu_id: -1 from device setting: cpu
2025-06-09 12:27:08.126 | INFO     | utils.logging:info:191 | === TRAINING CONFIGURATION ===
2025-06-09 12:27:08.127 | INFO     | utils.logging:info:191 | total_timesteps = 10000 (main training control - when to stop)
2025-06-09 12:27:08.127 | INFO     | utils.logging:info:191 | break_step = 10000 (ElegantRL equivalent of total_timesteps)
2025-06-09 12:27:08.127 | INFO     | utils.logging:info:191 | max_step = 1869 (per-episode limit - max steps per trading episode)
2025-06-09 12:27:08.127 | INFO     | utils.logging:info:191 | explore_step = 10000 (learning starts - from config)
2025-06-09 12:27:08.128 | INFO     | utils.logging:info:191 | eval_gap = 2500 (evaluation frequency - from eval_freq parameter)
2025-06-09 12:27:08.128 | INFO     | utils.logging:info:191 | horizon_len = 512 (steps per iteration - default)
2025-06-09 12:27:08.128 | INFO     | utils.logging:info:191 | === END CONFIGURATION ===
2025-06-09 12:29:04.921 | SUCCESS  | utils.logging:success:216 | SAC training completed: 10000 timesteps
2025-06-09 12:29:04.922 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 117.623s
2025-06-09 12:29:04.928 | ERROR    | utils.logging:error:201 | Trial failed: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (2,) + inhomogeneous part.
2025-06-09 12:29:04.983 | ERROR    | utils.logging:log_error_with_context:328 | Optimization failed: 
2025-06-09 12:29:04.983 | ERROR    | utils.logging:log_error_with_context:329 | No trials completed successfully out of 4 trials. All trials were pruned or failed.
Traceback (most recent call last):

  File "/app/workspaces/finrl-bot/sonet/main.py", line 1169, in <module>
    cli()
    └ <Group cli>

  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x7c4b780068e0>
           └ <Group cli>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x7c4b69cb58d0>
         │    └ <function Group.invoke at 0x7c4b780077e0>
         └ <Group cli>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x7c4b69cafcd0>
           │               │       │       └ <function Command.invoke at 0x7c4b780065c0>
           │               │       └ <Command tune>
           │               └ <click.core.Context object at 0x7c4b69cafcd0>
           └ <function Group.invoke.<locals>._process_result at 0x7c4b6a8b3420>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'trials': 1, 'study_name': None, 'resume': False}
           │   │      │    │           └ <click.core.Context object at 0x7c4b69cafcd0>
           │   │      │    └ <function tune at 0x7c4b69c91bc0>
           │   │      └ <Command tune>
           │   └ <function Context.invoke at 0x7c4b78005800>
           └ <click.core.Context object at 0x7c4b69cafcd0>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'trials': 1, 'study_name': None, 'resume': False}
           │         └ ()
           └ <function tune at 0x7c4b69c91bc0>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'trials': 1, 'study_name': None, 'resume': False}
           │ │                       └ ()
           │ └ <function get_current_context at 0x7c4b77fe25c0>
           └ <function tune at 0x7c4b69c91b20>

  File "/app/workspaces/finrl-bot/sonet/main.py", line 625, in tune
    results = optimizer.optimize(
              │         └ <function HyperparameterOptimizer.optimize at 0x7c4b2f89e980>
              └ <models.optimization.HyperparameterOptimizer object at 0x7c4b2f53f010>

  File "/app/workspaces/finrl-bot/sonet/src/utils/logging.py", line 388, in wrapper
    result = func(*args, **kwargs)
             │     │       └ {'objective': <models.optimization.SingleObjective object at 0x7c4b2f5e6990>, 'save_dir': 'models/checkpoints'}
             │     └ (<models.optimization.HyperparameterOptimizer object at 0x7c4b2f53f010>,)
             └ <function HyperparameterOptimizer.optimize at 0x7c4b2f89e8e0>

> File "/app/workspaces/finrl-bot/sonet/src/models/optimization.py", line 383, in optimize
    raise ValueError(f"No trials completed successfully out of {len(self.study.trials)} trials. All trials were pruned or failed.")

ValueError: No trials completed successfully out of 4 trials. All trials were pruned or failed.
2025-06-09 12:29:04.986 | SUCCESS  | utils.logging:__exit__:352 | Completed Hyperparameter Optimization in 117.923s
2025-06-09 12:29:04.986 | INFO     | utils.logging:wrapper:392 | Performance - HyperparameterOptimizer.optimize: 117.924s
2025-06-09 12:29:04.986 | ERROR    | __main__:tune:647 | Hyperparameter tuning failed: Optimization failed: No trials completed successfully out of 4 trials. All trials were pruned or failed.
Traceback (most recent call last):

  File "/app/workspaces/finrl-bot/sonet/main.py", line 1169, in <module>
    cli()
    └ <Group cli>

  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x7c4b780068e0>
           └ <Group cli>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x7c4b69cb58d0>
         │    └ <function Group.invoke at 0x7c4b780077e0>
         └ <Group cli>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x7c4b69cafcd0>
           │               │       │       └ <function Command.invoke at 0x7c4b780065c0>
           │               │       └ <Command tune>
           │               └ <click.core.Context object at 0x7c4b69cafcd0>
           └ <function Group.invoke.<locals>._process_result at 0x7c4b6a8b3420>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'trials': 1, 'study_name': None, 'resume': False}
           │   │      │    │           └ <click.core.Context object at 0x7c4b69cafcd0>
           │   │      │    └ <function tune at 0x7c4b69c91bc0>
           │   │      └ <Command tune>
           │   └ <function Context.invoke at 0x7c4b78005800>
           └ <click.core.Context object at 0x7c4b69cafcd0>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'trials': 1, 'study_name': None, 'resume': False}
           │         └ ()
           └ <function tune at 0x7c4b69c91bc0>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/click/decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'trials': 1, 'study_name': None, 'resume': False}
           │ │                       └ ()
           │ └ <function get_current_context at 0x7c4b77fe25c0>
           └ <function tune at 0x7c4b69c91b20>

> File "/app/workspaces/finrl-bot/sonet/main.py", line 631, in tune
    raise Exception(f"Optimization failed: {results.get('error', 'unknown error')}")

Exception: Optimization failed: No trials completed successfully out of 4 trials. All trials were pruned or failed.
