"""Asymmetric Trading Environment for FinRL.

This module implements a custom trading environment that targets asymmetric return profiles,
where the strategy aims to capture more upside than downside risk.

Key Features:
- Asymmetric reward function favoring upside returns
- Dynamic position sizing based on market conditions
- Risk-adjusted action scaling
- Enhanced state representation with asymmetry indicators
- Integration with FinRL framework
"""

import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, List, Optional, Tuple, Any
import warnings
from datetime import datetime
import traceback

try:
    from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv
except ImportError:
    # Fallback if FinRL is not available
    class StockTradingEnv:
        def __init__(self, *args, **kwargs):
            raise ImportError("FinRL not available. Please install FinRL.")

# Import settings for centralized configuration
try:
    from config.settings import Settings
    settings = Settings()
except ImportError:
    # Fallback if settings not available
    settings = None

from utils.logging import get_logger
from strategies.asymmetric_strategy import AsymmetricConfig


class AsymmetricTradingEnv(StockTradingEnv):
    """Asymmetric trading environment extending FinRL's StockTradingEnv.
    
    This environment implements an asymmetric return profile strategy that:
    1. Rewards upside movements more than it penalizes downside movements
    2. Adjusts position sizing based on market volatility and momentum
    3. Incorporates risk-adjusted reward scaling
    4. Provides enhanced state representation for asymmetric decision making
    """
    
    def __init__(
        self,
        df: pd.DataFrame,
        stock_dim: int,
        hmax: int,
        initial_amount: int,
        num_stock_shares: List[int],
        buy_cost_pct: List[float],
        sell_cost_pct: List[float],
        reward_scaling: float = 1e-4,
        state_space: int = None,
        action_space: int = None,
        tech_indicator_list: List[str] = None,
        turbulence_threshold: Optional[float] = None,
        risk_indicator_col: str = "turbulence",
        make_plots: bool = False,
        print_verbosity: int = 10,
        day: int = 0,
        initial: bool = True,
        previous_state: List = None,
        model_name: str = "",
        mode: str = "",
        iteration: str = "",
        # Asymmetric-specific parameters
        asymmetric_config: Optional[AsymmetricConfig] = None,  # Use the config object
        log_level: str = "INFO",
        **kwargs
    ):
        """Initialize the asymmetric trading environment.
        
        Args:
            df: Market data DataFrame
            stock_dim: Number of stocks
            hmax: Maximum number of shares to trade
            initial_amount: Initial cash amount
            num_stock_shares: Initial stock holdings
            buy_cost_pct: Buy transaction costs per stock
            sell_cost_pct: Sell transaction costs per stock
            reward_scaling: Reward scaling factor
            state_space: State space dimension
            action_space: Action space dimension
            tech_indicator_list: List of technical indicators
            turbulence_threshold: Turbulence threshold for risk management
            risk_indicator_col: Column name for risk indicator
            make_plots: Whether to generate plots
            print_verbosity: Printing frequency
            day: Starting day
            initial: Whether this is initial setup
            previous_state: Previous state for continuation
            model_name: Model name for logging
            mode: Mode (train/test)
            iteration: Iteration identifier
            asymmetric_ratio: Target upside/downside ratio
            volatility_lookback: Lookback period for volatility calculation
            momentum_threshold: Threshold for momentum detection
            risk_adjustment_factor: Factor for risk adjustment
            upside_reward_multiplier: Multiplier for upside rewards
            downside_penalty_multiplier: Multiplier for downside penalties
        """
        
        # Set default values if not provided
        if tech_indicator_list is None:
            # Use centralized tech indicator list from settings
            if settings is not None:
                tech_indicator_list = settings.data.tech_indicator_list
            else:
                # Fallback to comprehensive list if settings unavailable (using original column names)
                tech_indicator_list = [
                    'sma_5', 'sma_10', 'sma_20', 'sma_50',
                    'ema_12', 'ema_26',
                    'rsi_14', 'cci_20',
                    'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9',
                    'adx_14', 'dmp_14', 'dmn_14',
                    'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0',
                    'obv',
                    'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d',
                    'volume_ma_20', 'volume_ratio', 'volatility_20d',
                    'vix_ma_5', 'vix_ma_20', 'vix_percentile_252',
                    'vix_change', 'vix_change_5d', 'vix_regime_numeric',
                    'turbulence'
                ]
        
        if previous_state is None:
            previous_state = []
            
        # Calculate state and action space if not provided
        if state_space is None:
            # CRITICAL FIX: Calculate state space based on actual parent environment dimensions
            # The parent StockTradingEnv calculates: cash + holdings + prices + tech_indicators
            # We need to match this exactly and then add our asymmetric features
            base_state = 1 + 2 * stock_dim  # cash + holdings + prices
            tech_state = len(tech_indicator_list) * stock_dim  # tech indicators per stock
            asymmetric_state = stock_dim * 3  # volatility + momentum + asymmetry_score per stock
            state_space = base_state + tech_state + asymmetric_state

        if action_space is None:
            action_space = stock_dim  # one action per stock
        
        # Store asymmetric parameters from config
        if asymmetric_config is None:
            asymmetric_config = AsymmetricConfig(symbols=[])
        self.asymmetric_ratio = asymmetric_config.target_upside_downside_ratio
        self.volatility_lookback = asymmetric_config.volatility_lookback
        self.momentum_threshold = asymmetric_config.momentum_threshold
        self.risk_adjustment_factor = 0.5  # Fixed value for risk adjustment
        self.upside_reward_multiplier = 1.5  # Fixed value for upside reward
        self.downside_penalty_multiplier = 1.0  # Fixed value for downside penalty
        
        # Initialize logger with specified log level
        # CRITICAL FIX: Use standard Python logger for pickle compatibility
        from utils.logging import get_standard_logger
        self.log_level = log_level
        self.logger = get_standard_logger(self.__class__.__name__, level=log_level)

        # Initialize parent class first
        try:
            super().__init__(
                df=df,
                stock_dim=stock_dim,
                hmax=hmax,
                initial_amount=initial_amount,
                num_stock_shares=num_stock_shares,
                buy_cost_pct=buy_cost_pct,
                sell_cost_pct=sell_cost_pct,
                reward_scaling=reward_scaling,
                state_space=state_space,
                action_space=action_space,
                tech_indicator_list=tech_indicator_list,
                turbulence_threshold=turbulence_threshold,
                risk_indicator_col=risk_indicator_col,
                make_plots=make_plots,
                print_verbosity=print_verbosity,
                day=day,
                initial=initial,
                previous_state=previous_state,
                model_name=model_name,
                mode=mode,
                iteration=iteration
            )

            # CRITICAL FIX: Ensure df attribute is properly set after parent initialization
            # The parent class should have set this, but let's make sure
            if not hasattr(self, 'df') or self.df is None:
                self.df = df
                self.logger.warning("df attribute was not set by parent class, setting it manually")

        except Exception as e:
            self.logger.error(f"Failed to initialize parent StockTradingEnv: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            raise
        
        # CRITICAL FIX: Adjust state space based on actual parent state dimensions
        # The parent StockTradingEnv might calculate state space differently than our estimate
        try:
            # Force a reset to get the actual parent state
            parent_state = super().reset()
            if isinstance(parent_state, tuple):
                parent_state = parent_state[0]  # Extract state from (state, info) tuple

            if parent_state is not None:
                actual_parent_state_size = len(parent_state)
                asymmetric_features_size = stock_dim * 3  # Our asymmetric features
                actual_enhanced_state_space = actual_parent_state_size + asymmetric_features_size

                self.logger.info(f"State dimension adjustment: parent={actual_parent_state_size}, asymmetric={asymmetric_features_size}, total={actual_enhanced_state_space}")

                # Update our state_space to match reality
                self.state_space = actual_enhanced_state_space
                enhanced_state_space = actual_enhanced_state_space
            else:
                # Fallback to calculated value
                enhanced_state_space = state_space
                self.logger.warning(f"Using calculated state space: {enhanced_state_space}")
        except Exception as e:
            self.logger.warning(f"Could not adjust state space: {e}. Using calculated value: {state_space}")
            enhanced_state_space = state_space

        # Override observation_space to match the enhanced state space
        # The enhanced state includes base state + asymmetric features
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(enhanced_state_space,),
            dtype=np.float32
        )
        
        # Asymmetric-specific state tracking
        self.price_history = {}
        self.volatility_history = {}
        self.momentum_history = {}
        self.asymmetry_scores = {}
        self.previous_portfolio_value = self.initial_amount
        
        # Initialize price history with current prices
        self._initialize_price_history()
        
        self.logger.info(f"AsymmetricTradingEnv initialized with {stock_dim} stocks")
        self.logger.info(f"Asymmetric ratio: {self.asymmetric_ratio}, Volatility lookback: {self.volatility_lookback}")

    def _initialize_price_history(self):
        """Initialize price history for volatility and momentum calculations."""
        try:
            if hasattr(self, 'data') and self.data is not None:
                if len(self.df.tic.unique()) > 1:
                    # Multiple stocks
                    for i, tic in enumerate(self.df.tic.unique()):
                        if i < len(self.state[1:1+self.stock_dim]):
                            price = self.state[1 + i]
                            self.price_history[tic] = [price] * self.volatility_lookback
                            self.volatility_history[tic] = [0.0]
                            self.momentum_history[tic] = [0.0]
                            self.asymmetry_scores[tic] = 0.0
                else:
                    # Single stock
                    tic = self.df.tic.unique()[0]
                    price = self.state[1]
                    self.price_history[tic] = [price] * self.volatility_lookback
                    self.volatility_history[tic] = [0.0]
                    self.momentum_history[tic] = [0.0]
                    self.asymmetry_scores[tic] = 0.0
        except Exception as e:
            self.logger.warning(f"Could not initialize price history: {e}")
            # Initialize with empty dictionaries as fallback
            for i in range(self.stock_dim):
                tic = f"stock_{i}"
                self.price_history[tic] = [100.0] * self.volatility_lookback  # Default price
                self.volatility_history[tic] = [0.0]
                self.momentum_history[tic] = [0.0]
                self.asymmetry_scores[tic] = 0.0
    
    def _update_price_history(self):
        """Update price history for volatility and momentum calculations."""
        try:
            if len(self.df.tic.unique()) > 1:
                # Multiple stocks
                for i, tic in enumerate(self.df.tic.unique()):
                    if i < len(self.state[1:1+self.stock_dim]):
                        current_price = self.state[1 + i]
                        if tic in self.price_history:
                            self.price_history[tic].append(current_price)
                            if len(self.price_history[tic]) > self.volatility_lookback:
                                self.price_history[tic].pop(0)
            else:
                # Single stock
                tic = self.df.tic.unique()[0]
                current_price = self.state[1]
                if tic in self.price_history:
                    self.price_history[tic].append(current_price)
                    if len(self.price_history[tic]) > self.volatility_lookback:
                        self.price_history[tic].pop(0)
        except Exception as e:
            self.logger.warning(f"Could not update price history: {e}")
    
    def _calculate_volatility(self, tic: str) -> float:
        """Calculate volatility for a given stock.
        
        Args:
            tic: Stock ticker symbol
            
        Returns:
            Volatility measure
        """
        self.logger.debug(f"DEBUG: _calculate_volatility called for {tic}")
        try:
            if not hasattr(self, 'price_history') or tic not in self.price_history or len(self.price_history[tic]) < 2:
                self.logger.debug(f"DEBUG: _calculate_volatility returning 0.0 for {tic} (insufficient data)")
                return 0.0
            
            prices = np.array(self.price_history[tic])
            returns = np.diff(prices) / prices[:-1]
            volatility = np.std(returns) if len(returns) > 1 else 0.0
            
            # Update volatility history
            if tic in self.volatility_history:
                self.volatility_history[tic].append(volatility)
                if len(self.volatility_history[tic]) > 10:  # Keep last 10 volatility measures
                    self.volatility_history[tic].pop(0)
            
            self.logger.debug(f"DEBUG: _calculate_volatility for {tic}: {volatility}")
            return volatility
        except Exception as e:
            self.logger.warning(f"Could not calculate volatility for {tic}: {e}")
            self.logger.debug(f"DEBUG: _calculate_volatility returning 0.0 for {tic} (exception)")
            return 0.0
    
    def _calculate_momentum(self, tic: str) -> float:
        """Calculate momentum for a given stock.
        
        Args:
            tic: Stock ticker symbol
            
        Returns:
            Momentum measure
        """
        self.logger.debug(f"DEBUG: _calculate_momentum called for {tic}")
        try:
            if not hasattr(self, 'price_history') or tic not in self.price_history or len(self.price_history[tic]) < 5:
                self.logger.debug(f"DEBUG: _calculate_momentum returning 0.0 for {tic} (insufficient data)")
                return 0.0
            
            prices = np.array(self.price_history[tic])
            # Calculate momentum as percentage change over lookback period
            momentum = (prices[-1] - prices[0]) / prices[0] if prices[0] != 0 else 0.0
            
            # Update momentum history
            if tic in self.momentum_history:
                self.momentum_history[tic].append(momentum)
                if len(self.momentum_history[tic]) > 10:  # Keep last 10 momentum measures
                    self.momentum_history[tic].pop(0)
            
            self.logger.debug(f"DEBUG: _calculate_momentum for {tic}: {momentum}")
            return momentum
        except Exception as e:
            self.logger.warning(f"Could not calculate momentum for {tic}: {e}")
            self.logger.debug(f"DEBUG: _calculate_momentum returning 0.0 for {tic} (exception)")
            return 0.0
    
    def _calculate_asymmetry_score(self, tic: str) -> float:
        """Calculate asymmetry score for a given stock.
        
        Args:
            tic: Stock ticker symbol
            
        Returns:
            Asymmetry score (higher = more favorable for asymmetric strategy)
        """
        self.logger.debug(f"DEBUG: _calculate_asymmetry_score called for {tic}")
        try:
            if not hasattr(self, 'price_history') or tic not in self.price_history or len(self.price_history[tic]) < 3:
                self.logger.debug(f"DEBUG: _calculate_asymmetry_score returning 0.0 for {tic} (insufficient data)")
                return 0.0
            
            prices = np.array(self.price_history[tic])
            returns = np.diff(prices) / prices[:-1]
            
            if len(returns) < 2:
                self.logger.debug(f"DEBUG: _calculate_asymmetry_score returning 0.0 for {tic} (insufficient returns)")
                return 0.0
            
            # Calculate upside and downside returns
            upside_returns = returns[returns > 0]
            downside_returns = returns[returns < 0]
            
            if len(upside_returns) == 0 or len(downside_returns) == 0:
                self.logger.debug(f"DEBUG: _calculate_asymmetry_score returning 0.0 for {tic} (no upside or downside returns)")
                return 0.0
            
            # Calculate asymmetry score based on upside/downside ratio
            avg_upside = np.mean(upside_returns)
            avg_downside = np.abs(np.mean(downside_returns))
            
            asymmetry_score = avg_upside / avg_downside if avg_downside != 0 else 0.0
            
            # Normalize score relative to target ratio
            normalized_score = asymmetry_score / self.asymmetric_ratio
            
            self.asymmetry_scores[tic] = normalized_score
            
            self.logger.debug(f"DEBUG: _calculate_asymmetry_score for {tic}: {normalized_score}")
            return normalized_score
            
        except Exception as e:
            self.logger.warning(f"Could not calculate asymmetry score for {tic}: {e}")
            self.logger.debug(f"DEBUG: _calculate_asymmetry_score returning 0.0 for {tic} (exception)")
            return 0.0
    
    def _get_asymmetric_state_features(self) -> np.ndarray:
        """Get asymmetric-specific state features.
        
        Returns:
            Array of asymmetric features
        """
        self.logger.debug("DEBUG: _get_asymmetric_state_features called")
        features = []
        
        try:
            tickers = self.df.tic.unique() if len(self.df.tic.unique()) > 1 else [self.df.tic.unique()[0]]
            self.logger.debug(f"DEBUG: Processing {len(tickers)} tickers: {tickers[:self.stock_dim]}")
            
            for tic in tickers[:self.stock_dim]:  # Ensure we don't exceed stock_dim
                volatility = self._calculate_volatility(tic)
                momentum = self._calculate_momentum(tic)
                asymmetry = self._calculate_asymmetry_score(tic)
                
                features.extend([volatility, momentum, asymmetry])
                self.logger.debug(f"DEBUG: Features for {tic}: vol={volatility}, mom={momentum}, asym={asymmetry}")
            
            # Pad with zeros if we have fewer stocks than stock_dim
            while len(features) < self.stock_dim * 3:
                features.extend([0.0, 0.0, 0.0])
                
            self.logger.debug(f"DEBUG: Final asymmetric features array length: {len(features)}")
                
        except Exception as e:
            self.logger.warning(f"Could not calculate asymmetric features: {e}")
            self.logger.debug(f"DEBUG: _get_asymmetric_state_features returning zeros due to exception")
            # Return zeros as fallback
            features = [0.0] * (self.stock_dim * 3)
        
        return np.array(features)
    
    def _update_state(self):
        """Update state with asymmetric features."""
        try:
            # Get base state from parent class
            base_state = super()._update_state()
            
            if base_state is None:
                self.logger.error("Base state is None from parent class")
                return None
            
            # Update price history
            self._update_price_history()
            
            # Get asymmetric features
            asymmetric_features = self._get_asymmetric_state_features()
            
            # Combine base state with asymmetric features
            enhanced_state = np.concatenate([base_state, asymmetric_features])
            
            return enhanced_state
            
        except Exception as e:
            self.logger.error(f"Error in _update_state: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def _initiate_state(self):
        """Initialize state with asymmetric features."""
        try:
            # Get base state from parent class
            base_state = super()._initiate_state()
            
            if base_state is None:
                self.logger.error("Base state is None from parent class")
                return None
            
            # Initialize asymmetric features
            asymmetric_features = self._get_asymmetric_state_features()
            
            # Combine base state with asymmetric features
            enhanced_state = np.concatenate([base_state, asymmetric_features])
            
            return enhanced_state
            
        except Exception as e:
            self.logger.error(f"Error in _initiate_state: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    

    def _get_state(self) -> np.ndarray:
        """Get enhanced state with asymmetric features.
        
        Returns:
            Enhanced state array including asymmetric features
        """
        self.logger.debug("DEBUG: _get_state called")
        try:
            # Get base state from parent class
            if hasattr(self, 'state') and self.state is not None:
                self.logger.debug(f"DEBUG: self.state type: {type(self.state)}, shape: {getattr(self.state, 'shape', 'N/A')}, dtype: {getattr(self.state, 'dtype', 'N/A')}")
                if isinstance(self.state, np.ndarray) and self.state.size < 20: # Log content if small
                    self.logger.debug(f"DEBUG: self.state content: {self.state}")
                elif not isinstance(self.state, np.ndarray):
                     self.logger.debug(f"DEBUG: self.state content (non-ndarray): {self.state}")

                base_state = np.array(self.state) # Attempt to convert to NumPy array
                self.logger.debug(f"DEBUG: base_state (from np.array(self.state)) type: {type(base_state)}, shape: {base_state.shape}, dtype: {base_state.dtype}")
                if base_state.size < 20: # Log content if small
                    self.logger.debug(f"DEBUG: base_state content: {base_state}")
            else:
                self.logger.error("No base state (self.state) available or is None")
                return np.zeros(self.state_space)
            
            # Update price history with current prices
            self._update_price_history()
            
            # Get asymmetric features
            asymmetric_features = self._get_asymmetric_state_features()
            self.logger.debug(f"DEBUG: asymmetric_features type: {type(asymmetric_features)}, shape: {asymmetric_features.shape}, dtype: {asymmetric_features.dtype}")
            if asymmetric_features.size < 10:
                 self.logger.debug(f"DEBUG: asymmetric_features content: {asymmetric_features}")
            
            # Combine base state with asymmetric features
            self.logger.debug(f"DEBUG: Attempting concatenation with base_state (shape: {base_state.shape}, dtype: {base_state.dtype}) and asymmetric_features (shape: {asymmetric_features.shape}, dtype: {asymmetric_features.dtype})")
            enhanced_state = np.concatenate([base_state, asymmetric_features])
            
            self.logger.debug(f"DEBUG: enhanced_state type: {type(enhanced_state)}, shape: {enhanced_state.shape}, dtype: {enhanced_state.dtype}, expected state_space: {self.state_space}")
            if enhanced_state.size < 30:
                 self.logger.debug(f"DEBUG: enhanced_state content: {enhanced_state}")
                       
            return enhanced_state
            
        except Exception as e:
            self.logger.error(f"Error in _get_state: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            # Return safe fallback
            return np.zeros(self.state_space)
    
    def step(self, actions):
        """Execute one step in the environment with asymmetric reward calculation.

        Args:
            actions: Trading actions

        Returns:
            Tuple of (state, reward, terminal, truncated, info)
        """
        try:
            # Convert actions to numpy array if needed
            if not isinstance(actions, np.ndarray):
                actions = np.array(actions)

            current_day = getattr(self, 'day', 'unknown')
            self.logger.debug(f"STEP {current_day} START - Actions: {actions}")

            portfolio_before_step = 0.0
            cash_before_step = 0.0
            holdings_before_step = np.zeros(self.stock_dim)
            prices_before_step = np.zeros(self.stock_dim)

            try:
                if hasattr(self, 'state') and self.state is not None and len(self.state) > 2*self.stock_dim:
                    cash_before_step = self.state[0]
                    holdings_before_step = self.state[1:self.stock_dim+1]
                    prices_before_step = self.state[self.stock_dim+1:2*self.stock_dim+1]
                    portfolio_before_step = cash_before_step + np.sum(holdings_before_step * prices_before_step)
                    # self.logger.debug(f"STEP {current_day}: BEFORE - Portfolio: ${portfolio_before_step:.2f}, Cash: ${cash_before_step:.2f}, Holdings: {holdings_before_step}")
                else:
                    self.logger.warning(f"STEP {current_day}: BEFORE - Could not reliably get portfolio state before super().step(). State: {getattr(self, 'state', 'N/A')}")
            except Exception as e:
                self.logger.warning(f"STEP {current_day}: BEFORE - Error logging portfolio state: {e}")

            # Execute parent step to get base reward
            state, base_reward, terminal, truncated, info = super().step(actions)
            base_reward = float(base_reward) # Ensure it's a float

            self.logger.debug(f"STEP {current_day}: SUPER().STEP - Base Reward: {base_reward:.8f}, Terminal: {terminal}, Truncated: {truncated}")
            self.logger.debug(f"STEP {current_day}: SUPER().STEP - Info from parent (raw): {info}")
            self.logger.debug(f"STEP {current_day}: SUPER().STEP - Info from parent (type): {type(info)}")
            if isinstance(info, dict):
                for key, value in info.items():
                    try:
                        value_str = str(value)
                        # Truncate if very long, especially for arrays/dataframes
                        if len(value_str) > 200 and hasattr(value, 'shape'): # Heuristic for large array-like
                            value_preview = f"Object of type {type(value)} with shape {getattr(value, 'shape', 'N/A')}"
                        elif len(value_str) > 200:
                            value_preview = value_str[:197] + "..."
                        else:
                            value_preview = value_str
                        self.logger.debug(f"STEP {current_day}: SUPER().STEP - Info item: key='{key}', type='{type(value)}', value_preview='{value_preview}'")
                    except Exception as e_log_info:
                        self.logger.warning(f"STEP {current_day}: SUPER().STEP - Error logging info item '{key}': {e_log_info}")
            elif info is not None:
                self.logger.debug(f"STEP {current_day}: SUPER().STEP - Info from parent is not a dict, actual value: {str(info)[:200]}")


            portfolio_after_step = 0.0
            cash_after_step = 0.0
            holdings_after_step = np.zeros(self.stock_dim)
            prices_after_step = np.zeros(self.stock_dim)
            try:
                if state is not None and len(state) > 2*self.stock_dim:
                    cash_after_step = state[0]
                    holdings_after_step = state[1:self.stock_dim+1]
                    prices_after_step = state[self.stock_dim+1:2*self.stock_dim+1]
                    portfolio_after_step = cash_after_step + np.sum(holdings_after_step * prices_after_step)
                    portfolio_change = portfolio_after_step - portfolio_before_step if portfolio_before_step else 0.0
                    # self.logger.debug(f"STEP {current_day}: AFTER - Portfolio: ${portfolio_after_step:.2f}, Cash: ${cash_after_step:.2f}, Holdings: {holdings_after_step}, Change: ${portfolio_change:.2f}")
                else:
                    self.logger.warning(f"STEP {current_day}: AFTER - State is None or too short after super().step(). State: {state}")

            except Exception as e:
                self.logger.warning(f"STEP {current_day}: AFTER - Error logging portfolio state: {e}")

            if state is None:
                self.logger.error(f"STEP {current_day}: State is None after parent step. Returning safe defaults.")
                # Attempt to get a valid state, otherwise use zeros
                safe_state = self._get_state() if hasattr(self, '_get_state') else np.zeros(self.observation_space.shape)
                return safe_state, 0.0, True, False, {}

            # Ensure info is a dict
            if not isinstance(info, dict):
                info = {}

            # Add portfolio value to info dict for backtesting
            try:
                current_total_asset = 0.0
                if hasattr(self, 'asset_memory') and self.asset_memory and len(self.asset_memory) > 0:
                    current_total_asset = float(self.asset_memory[-1])
                elif portfolio_after_step > 0: # Use calculated portfolio_after_step if asset_memory is not up-to-date
                    current_total_asset = portfolio_after_step
                
                info['total_asset'] = current_total_asset
                self.logger.debug(f"STEP {current_day}: INFO - total_asset set to: {info['total_asset']:.2f}")

                # Ensure asset_memory is updated if it was used to derive total_asset from portfolio_after_step
                if not (hasattr(self, 'asset_memory') and self.asset_memory and len(self.asset_memory) > 0) and portfolio_after_step > 0:
                    if not hasattr(self, 'asset_memory') or not self.asset_memory:
                        self.asset_memory = [self.initial_amount]
                    self.asset_memory.append(portfolio_after_step)
                    self.logger.debug(f"STEP {current_day}: INFO - asset_memory updated with: {portfolio_after_step:.2f}")

            except Exception as e:
                self.logger.warning(f"STEP {current_day}: INFO - Could not add/verify portfolio value in info: {e}")

            # Get enhanced state with asymmetric features
            enhanced_state = self._get_state()
            if enhanced_state is None:
                self.logger.error(f"STEP {current_day}: enhanced_state is None from _get_state. Returning safe defaults.")
                safe_state = np.zeros(self.observation_space.shape)
                return safe_state, 0.0, True, False, info # return info as is
            
            self.logger.debug(f"STEP {current_day}: Enhanced state shape: {enhanced_state.shape if enhanced_state is not None else 'None'}")

            # Apply asymmetric reward shaping
            market_trend = self._detect_market_trend()
            shaped_reward = self._apply_asymmetric_reward_shaping(base_reward)
            self.logger.debug(f"STEP {current_day}: REWARD SHAPING - Market Trend: {market_trend}, Base Reward: {base_reward:.8f}, Shaped Reward: {shaped_reward:.8f}")

            self.logger.debug(f"STEP {current_day} END - Returning: Shaped Reward: {shaped_reward:.8f}, Terminal: {terminal}, Truncated: {truncated}, Info: {{}}")
            return enhanced_state, shaped_reward, terminal, truncated, {}

        except Exception as e:
            current_day_except = getattr(self, 'day', 'unknown_in_exception')
            self.logger.error(f"STEP {current_day_except}: CRITICAL ERROR in step method: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            # Return safe defaults
            safe_state_except = self._get_state() if hasattr(self, '_get_state') else np.zeros(self.observation_space.shape if hasattr(self, 'observation_space') and self.observation_space is not None else (10,)) # A small default shape if obs space unknown
            return safe_state_except, 0.0, True, False, {}

    def _apply_asymmetric_reward_shaping(self, base_reward: float) -> float:
        """Apply asymmetric reward shaping based on market trend.

        Args:
            base_reward: Base reward from environment (e.g., portfolio change)

        Returns:
            Asymmetric-shaped reward
        """
        try:
            market_trend = self._detect_market_trend()
            shaped_reward = base_reward  # Default to base reward

            if market_trend == 'down':
                if base_reward < 0:
                    shaped_reward = base_reward * 1.5  # Increase penalty
                else:
                    shaped_reward = base_reward * 0.8  # Reduce gains slightly
            elif market_trend == 'up':
                if base_reward > 0:
                    shaped_reward = base_reward * 1.3  # Increase reward
                else:
                    shaped_reward = base_reward * 0.9  # Reduce penalty slightly

            return shaped_reward

        except Exception as e:
            self.logger.warning(f"Failed to apply asymmetric reward shaping: {e}. Returning base_reward.")
            return base_reward

    def _detect_market_trend(self) -> str:
        """Detect current market trend based on recent portfolio performance.

        Returns:
            Market trend: 'up', 'down', or 'neutral'
        """
        try:
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 5:
                # Asset memory stores portfolio value at each step
                recent_values = np.array(self.asset_memory[-5:], dtype=np.float32)
                recent_diffs = np.diff(recent_values)

                if len(recent_diffs) == 0:
                    return 'neutral'

                avg_change = np.mean(recent_diffs)

                # Threshold relative to initial capital
                threshold = self.initial_amount * 0.0005  # 0.05% of initial capital

                if avg_change > threshold:
                    return 'up'
                elif avg_change < -threshold:
                    return 'down'
                else:
                    return 'neutral'

            return 'neutral'

        except Exception as e:
            self.logger.warning(f"Error detecting market trend: {e}. Defaulting to 'neutral'.")
            return 'neutral'

    def reset(self, *, seed=None, options=None):
        """Reset the environment.
        
        Args:
            seed: Random seed
            options: Reset options
            
        Returns:
            Tuple of (initial_state, info)
        """
        try:
            # Reset parent environment
            result = super().reset(seed=seed, options=options)
            # self.logger.debug(f"RESET: SUPER().RESET - Result from parent (raw): {result}")
            self.logger.debug(f"RESET: SUPER().RESET - Result from parent (type): {type(result)}")
            parent_info = {}
            obs_from_parent = None

            if isinstance(result, tuple) and len(result) == 2:
                obs_from_parent, parent_info = result
                self.logger.debug(f"RESET: SUPER().RESET - Obs from parent (type): {type(obs_from_parent)}")
                if hasattr(obs_from_parent, 'shape'):
                    self.logger.debug(f"RESET: SUPER().RESET - Obs from parent (shape): {obs_from_parent.shape}")
                self.logger.debug(f"RESET: SUPER().RESET - Info from parent (raw): {parent_info}")
                self.logger.debug(f"RESET: SUPER().RESET - Info from parent (type): {type(parent_info)}")
                if isinstance(parent_info, dict):
                    for key, value in parent_info.items():
                        try:
                            value_str = str(value)
                            if len(value_str) > 200 and hasattr(value, 'shape'):
                                value_preview = f"Object of type {type(value)} with shape {getattr(value, 'shape', 'N/A')}"
                            elif len(value_str) > 200:
                                value_preview = value_str[:197] + "..."
                            else:
                                value_preview = value_str
                            self.logger.debug(f"RESET: SUPER().RESET - Info item: key='{key}', type='{type(value)}', value_preview='{value_preview}'")
                        except Exception as e_log_info:
                            self.logger.warning(f"RESET: SUPER().RESET - Error logging info item '{key}': {e_log_info}")
                elif parent_info is not None:
                    self.logger.debug(f"RESET: SUPER().RESET - Info from parent is not a dict, actual value: {str(parent_info)[:200]}")
            elif isinstance(result, np.ndarray): # FinRL might just return obs
                obs_from_parent = result
                self.logger.debug(f"RESET: SUPER().RESET - Parent returned only an observation (np.ndarray). Shape: {obs_from_parent.shape}")
                parent_info = {} # No info dict returned by parent
            else:
                 self.logger.warning(f"RESET: SUPER().RESET - Parent returned unexpected result structure: {type(result)}. Full result: {str(result)[:200]}")
                 obs_from_parent = None # Or try to infer if possible
                 parent_info = {}

            # Reset asymmetric-specific state
            self.price_history = {}
            self.volatility_history = {}
            self.momentum_history = {}
            self.asymmetry_scores = {}
            self.previous_portfolio_value = self.initial_amount
            
            # Initialize price history
            self._initialize_price_history()
            
            # Get enhanced initial state with asymmetric features
            enhanced_state = self._get_state()
            
            # Return enhanced state with an empty info dict for ElegantRL compatibility
            return enhanced_state, {}
            
        except Exception as e:
            self.logger.error(f"Error in reset: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            raise
    
    def get_asymmetric_metrics(self) -> Dict[str, float]:
        """Get asymmetric strategy metrics.
        
        Returns:
            Dictionary of asymmetric metrics
        """
        metrics = {}
        
        try:
            # Calculate overall asymmetry score
            if self.asymmetry_scores:
                metrics['avg_asymmetry_score'] = np.mean(list(self.asymmetry_scores.values()))
                metrics['max_asymmetry_score'] = np.max(list(self.asymmetry_scores.values()))
                metrics['min_asymmetry_score'] = np.min(list(self.asymmetry_scores.values()))
            
            # Calculate average volatility
            if self.volatility_history:
                all_volatilities = []
                for vol_history in self.volatility_history.values():
                    if vol_history:
                        all_volatilities.extend(vol_history)
                if all_volatilities:
                    metrics['avg_volatility'] = np.mean(all_volatilities)
                    metrics['max_volatility'] = np.max(all_volatilities)
            
            # Calculate average momentum
            if self.momentum_history:
                all_momentum = []
                for mom_history in self.momentum_history.values():
                    if mom_history:
                        all_momentum.extend(mom_history)
                if all_momentum:
                    metrics['avg_momentum'] = np.mean(all_momentum)
                    metrics['positive_momentum_ratio'] = np.mean([m > 0 for m in all_momentum])
            
            # Portfolio metrics
            if hasattr(self, 'asset_memory') and len(self.asset_memory) > 1:
                returns = np.diff(self.asset_memory) / np.array(self.asset_memory[:-1])
                upside_returns = returns[returns > 0]
                downside_returns = returns[returns < 0]
                
                if len(upside_returns) > 0 and len(downside_returns) > 0:
                    metrics['realized_upside_downside_ratio'] = np.mean(upside_returns) / np.abs(np.mean(downside_returns))
                    metrics['upside_capture_ratio'] = len(upside_returns) / len(returns)
                
        except Exception as e:
            self.logger.warning(f"Error calculating asymmetric metrics: {e}")
        
        return metrics
